<?php

namespace App\Livewire;

use Livewire\Component;

class TestRichEditor extends Component
{
    public $content = '';
    public $content2 = '<p>This is <strong>pre-filled</strong> content with <em>formatting</em>.</p>';
    
    public function submit()
    {
        $this->validate([
            'content' => 'required|min:10',
        ]);
        
        session()->flash('message', 'Content saved successfully!');
        session()->flash('content_preview', $this->content);
    }
    
    public function render()
    {
        return view('livewire.test-rich-editor');
    }
}
