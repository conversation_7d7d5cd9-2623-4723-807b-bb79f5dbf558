/***********************/
/**    APP JS FILE    **/
/***********************/

import './bootstrap';


// import Alpine from 'alpinejs';

// alpine loaded by tailwind, enabling here double the opperation
// window.Alpine = Alpine;

import "./dropzone";
import "../css/dropzone.css";

// Rich Text Editor
import "./rich-text-editor";



// import
import focus from '@alpinejs/focus';
import mask from '@alpinejs/mask';
import morph from '@alpinejs/morph';
import collapse from '@alpinejs/collapse'


// Alpine Plugins
if (typeof Alpine !== 'undefined') {

    // Alpine Plugins 
    Alpine.plugin(focus);
    Alpine.plugin(mask); //field format set
    Alpine.plugin(morph); //chaning fragment
    Alpine.plugin(collapse); //collapsing divs
}


// human readable numbers like 1k,2m
import humanNumber from 'human-number';
window.humanNumber = humanNumber;

// const humanReadableNumber = humanNumber(number);
// console.log(humanReadableNumber); // Output: "1.23M"


// Fonts
import "@fontsource/inter/latin-400.css"; // Specify weight
import "@fontsource/inter/latin-500.css"; // Specify weight
import "@fontsource/inter/latin-600.css";
import "@fontsource/inter/latin-700.css";




// Alpine.start();
// import "@fontsource/inter/800.css";



document.addEventListener('livewire:initialized', function () {
    // User Logout Browser Broadcast
    const sessionId = document.head.querySelector('meta[name="session-id"]')?.content;

    if (sessionId) {
        window.Echo.private(`logout-session.${sessionId}`)
            .listen('.session.logout', () => {
                window.location.href = '/login';
            });
    }

});
