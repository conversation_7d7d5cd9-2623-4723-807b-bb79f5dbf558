/**
 * RICH TEXT EDITOR COMPONENT
 * 
 * TipTap-based rich text editor for Laravel Blade components
 * Integrates with Livewire and Alpine.js
 */

import { Editor } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Placeholder from '@tiptap/extension-placeholder';

// Make TipTap available globally for Alpine.js components
window.TiptapEditor = Editor;
window.TiptapStarterKit = StarterKit;
window.TiptapUnderline = Underline;
window.TiptapPlaceholder = Placeholder;

// Alpine.js component function for rich text editor
window.richTextEditor = function(config) {
    return {
        editor: null,
        content: '',
        
        async initEditor() {
            // Wait for Alpine to be ready
            await this.$nextTick();
            
            // Get initial content from Livewire if available
            if (config.wireModel && this.$wire) {
                this.content = this.$wire.get(config.wireModel) || '';
            }
            
            // Initialize TipTap editor
            this.editor = new window.TiptapEditor({
                element: this.$refs.editorElement,
                extensions: [
                    window.TiptapStarterKit,
                    window.TiptapUnderline,
                    window.TiptapPlaceholder.configure({
                        placeholder: config.placeholder,
                    }),
                ],
                content: this.content,
                editable: !config.disabled,
                onUpdate: ({ editor }) => {
                    this.content = editor.getHTML();
                    this.updateLivewire();
                },
            });
        },
        
        updateLivewire() {
            if (config.wireModel && this.$wire) {
                this.$wire.set(config.wireModel, this.content);
            }
            if (this.$refs.hiddenInput) {
                this.$refs.hiddenInput.value = this.content;
                this.$refs.hiddenInput.dispatchEvent(new Event('input'));
            }
        },
        
        toggleLink() {
            if (this.editor && this.editor.isActive('link')) {
                this.editor.chain().focus().unsetLink().run();
            } else {
                const url = window.prompt('Enter URL:');
                if (url && this.editor) {
                    this.editor.chain().focus().setLink({ href: url }).run();
                }
            }
        },
        
        destroy() {
            if (this.editor) {
                this.editor.destroy();
                this.editor = null;
            }
        }
    }
}
