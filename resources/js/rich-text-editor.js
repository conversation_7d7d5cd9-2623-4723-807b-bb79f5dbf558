/**
 * RICH TEXT EDITOR COMPONENT
 *
 * TipTap-based rich text editor for Laravel Blade components
 * Integrates with Livewire and Alpine.js
 */

import { Editor } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Placeholder from '@tiptap/extension-placeholder';

// Make TipTap available globally for Alpine.js components
window.TiptapEditor = Editor;
window.TiptapStarterKit = StarterKit;
window.TiptapUnderline = Underline;
window.TiptapPlaceholder = Placeholder;

// Alpine.js component function for rich text editor
window.richTextEditor = function(config) {
    return {
        editor: null,
        content: '',
        isInitialized: false,
        isUpdating: false,

        init() {
            // Initialize immediately when Alpine component is ready
            this.initEditor();
        },

        async initEditor() {
            try {
                console.log('Initializing rich text editor with config:', config);

                // Wait for DOM to be ready
                await this.$nextTick();

                // Check if editor element exists
                if (!this.$refs.editorElement) {
                    console.error('Editor element not found');
                    return;
                }

                // Get initial content from Livewire if available
                if (config.wireModel && this.$wire) {
                    try {
                        this.content = this.$wire.get(config.wireModel) || '';
                        console.log('Initial content from Livewire:', this.content);
                    } catch (e) {
                        console.warn('Could not get initial content from Livewire:', e);
                        this.content = '';
                    }
                }

                // Also check hidden input for initial content (for defer binding)
                if (this.$refs.hiddenInput && this.$refs.hiddenInput.value) {
                    this.content = this.$refs.hiddenInput.value;
                    console.log('Initial content from hidden input:', this.content);
                }

                // Initialize TipTap editor
                this.editor = new window.TiptapEditor({
                    element: this.$refs.editorElement,
                    extensions: [
                        window.TiptapStarterKit,
                        window.TiptapUnderline,
                        window.TiptapPlaceholder.configure({
                            placeholder: config.placeholder || 'Start typing...',
                        }),
                    ],
                    content: this.content,
                    editable: !config.disabled,
                    onUpdate: ({ editor }) => {
                        if (!this.isUpdating) {
                            this.content = editor.getHTML();
                            this.updateLivewire();
                        }
                    },
                    onCreate: ({ editor }) => {
                        console.log('TipTap editor created successfully');
                        this.isInitialized = true;
                    },
                    onDestroy: () => {
                        console.log('TipTap editor destroyed');
                        this.isInitialized = false;
                    }
                });

                console.log('TipTap editor initialized:', this.editor);

            } catch (error) {
                console.error('Failed to initialize rich text editor:', error);
            }
        },

        updateLivewire() {
            if (this.isUpdating) return;

            try {
                this.isUpdating = true;

                // Update Livewire property using defer method for better performance
                if (config.wireModel && this.$wire) {
                    // Use $wire.set for immediate updates or entangle for reactive binding
                    this.$wire.set(config.wireModel, this.content);
                }

                // Update hidden input for form submission and Livewire defer binding
                if (this.$refs.hiddenInput) {
                    this.$refs.hiddenInput.value = this.content;
                    // Dispatch both input and change events for better compatibility
                    this.$refs.hiddenInput.dispatchEvent(new Event('input', { bubbles: true }));
                    this.$refs.hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
                }

                console.log('Updated Livewire with content length:', this.content.length);

            } catch (error) {
                console.error('Failed to update Livewire:', error);
            } finally {
                // Reset flag after a short delay to prevent rapid updates
                setTimeout(() => {
                    this.isUpdating = false;
                }, 50);
            }
        },

        toggleLink() {
            if (!this.editor) {
                console.warn('Editor not initialized');
                return;
            }

            try {
                if (this.editor.isActive('link')) {
                    this.editor.chain().focus().unsetLink().run();
                } else {
                    const url = window.prompt('Enter URL:');
                    if (url) {
                        this.editor.chain().focus().setLink({ href: url }).run();
                    }
                }
            } catch (error) {
                console.error('Failed to toggle link:', error);
            }
        },

        destroy() {
            try {
                if (this.editor) {
                    this.editor.destroy();
                    this.editor = null;
                }
                this.isInitialized = false;
            } catch (error) {
                console.error('Failed to destroy editor:', error);
            }
        }
    }
}
