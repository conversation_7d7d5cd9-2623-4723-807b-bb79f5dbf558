@props([
    'wireModel' => null,
    'placeholder' => 'Start typing...',
    'disabled' => false,
    'error' => false,
    'errorMessage' => '',
    'id' => 'rich-text-editor-' . uniqid(),
])

<div 
    x-data="richTextEditor({
        wireModel: '{{ $wireModel }}',
        placeholder: '{{ $placeholder }}',
        disabled: {{ $disabled ? 'true' : 'false' }},
        id: '{{ $id }}'
    })"
    x-init="initEditor()"
    class="rich-text-editor border border-gray-300 rounded-lg overflow-hidden {{ $error ? 'border-red-500' : '' }}"
>
    <!-- Toolbar -->
    <div x-show="editor" class="border-b border-gray-200 bg-gray-50 p-2">
        <div class="flex flex-wrap items-center gap-1">
            <!-- Bold -->
            <button
                type="button"
                @click="editor.chain().focus().toggleBold().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('bold') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Bold (Ctrl+B)"
                :disabled="disabled"
            >
                <x-icons.lucide.bold class="w-4 h-4" />
            </button>

            <!-- Italic -->
            <button
                type="button"
                @click="editor.chain().focus().toggleItalic().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('italic') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Italic (Ctrl+I)"
                :disabled="disabled"
            >
                <x-icons.lucide.italic class="w-4 h-4" />
            </button>

            <!-- Underline -->
            <button
                type="button"
                @click="editor.chain().focus().toggleUnderline().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('underline') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Underline (Ctrl+U)"
                :disabled="disabled"
            >
                <x-icons.lucide.underline class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Bullet List -->
            <button
                type="button"
                @click="editor.chain().focus().toggleBulletList().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('bulletList') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Bullet List"
                :disabled="disabled"
            >
                <x-icons.lucide.list class="w-4 h-4" />
            </button>

            <!-- Numbered List -->
            <button
                type="button"
                @click="editor.chain().focus().toggleOrderedList().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('orderedList') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Numbered List"
                :disabled="disabled"
            >
                <x-icons.lucide.list-ordered class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Link -->
            <button
                type="button"
                @click="toggleLink()"
                :class="{ 'bg-gray-200': editor && editor.isActive('link') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Add Link"
                :disabled="disabled"
            >
                <x-icons.lucide.link class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Undo -->
            <button
                type="button"
                @click="editor.chain().focus().undo().run()"
                :disabled="disabled || !editor || !editor.can().undo()"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Undo (Ctrl+Z)"
            >
                <x-icons.lucide.undo class="w-4 h-4" />
            </button>

            <!-- Redo -->
            <button
                type="button"
                @click="editor.chain().focus().redo().run()"
                :disabled="disabled || !editor || !editor.can().redo()"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Redo (Ctrl+Y)"
            >
                <x-icons.lucide.redo class="w-4 h-4" />
            </button>
        </div>
    </div>

    <!-- Editor Content -->
    <div
        x-ref="editorElement"
        class="min-h-[150px] p-1 text-sm/6 text-gray-700 focus-within:ring-0 overflow-y-auto max-h-[200px]"
    ></div>

    <!-- Hidden input for Livewire binding -->
    @if($wireModel)
        <input type="hidden" wire:model.defer="{{ $wireModel }}" x-ref="hiddenInput" />
    @endif

    <!-- Error Message -->
    @if($error && $errorMessage)
        <div class="text-red-500 text-sm mt-1 px-4 pb-2">
            {{ $errorMessage }}
        </div>
    @endif
</div>

<script>
function richTextEditor(config) {
    return {
        editor: null,
        content: '',
        
        async initEditor() {
            // Wait for TipTap to be available
            if (typeof window.TiptapEditor === 'undefined') {
                await this.loadTiptap();
            }
            
            // Get initial content from Livewire if available
            if (config.wireModel && this.$wire) {
                this.content = this.$wire.get(config.wireModel) || '';
            }
            
            // Initialize TipTap editor
            this.editor = new window.TiptapEditor({
                element: this.$refs.editorElement,
                extensions: [
                    window.TiptapStarterKit,
                    window.TiptapUnderline,
                    window.TiptapPlaceholder.configure({
                        placeholder: config.placeholder,
                    }),
                ],
                content: this.content,
                editable: !config.disabled,
                onUpdate: ({ editor }) => {
                    this.content = editor.getHTML();
                    this.updateLivewire();
                },
            });
        },
        
        updateLivewire() {
            if (config.wireModel && this.$wire) {
                this.$wire.set(config.wireModel, this.content);
            }
            if (this.$refs.hiddenInput) {
                this.$refs.hiddenInput.value = this.content;
                this.$refs.hiddenInput.dispatchEvent(new Event('input'));
            }
        },
        
        toggleLink() {
            if (this.editor.isActive('link')) {
                this.editor.chain().focus().unsetLink().run();
            } else {
                const url = window.prompt('Enter URL:');
                if (url) {
                    this.editor.chain().focus().setLink({ href: url }).run();
                }
            }
        },
        
        async loadTiptap() {
            // Dynamic import of TipTap modules
            const [
                { Editor },
                StarterKit,
                Underline,
                Placeholder
            ] = await Promise.all([
                import('@tiptap/core'),
                import('@tiptap/starter-kit'),
                import('@tiptap/extension-underline'),
                import('@tiptap/extension-placeholder')
            ]);
            
            window.TiptapEditor = Editor;
            window.TiptapStarterKit = StarterKit.default;
            window.TiptapUnderline = Underline.default;
            window.TiptapPlaceholder = Placeholder.default;
        }
    }
}
</script>

<style>
/* Basic editor styling */
.rich-text-editor .ProseMirror {
    outline: none;
    min-height: 120px;
    padding: 0.5rem;
}

/* Placeholder styling */
.rich-text-editor .ProseMirror p.is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #9ca3af;
    pointer-events: none;
    height: 0;
}

/* Basic list styling */
.rich-text-editor .ProseMirror ul {
    list-style-type: disc;
    margin-left: 1.5rem;
}

.rich-text-editor .ProseMirror ol {
    list-style-type: decimal;
    margin-left: 1.5rem;
}

.rich-text-editor .ProseMirror li {
    margin: 0.25rem 0;
}

/* Focus styles */
.rich-text-editor:focus-within {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}
</style>
