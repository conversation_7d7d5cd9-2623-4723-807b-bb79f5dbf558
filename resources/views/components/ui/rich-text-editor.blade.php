@props([
    'wireModel' => null,
    'placeholder' => 'Start typing...',
    'disabled' => false,
    'error' => false,
    'errorMessage' => '',
    'id' => 'rich-text-editor-' . uniqid(),
])

<div
    x-data="richTextEditor({
        wireModel: '{{ $wireModel }}',
        placeholder: '{{ addslashes($placeholder) }}',
        disabled: {{ $disabled ? 'true' : 'false' }},
        id: '{{ $id }}'
    })"
    class="rich-text-editor border border-gray-300 rounded-lg overflow-hidden {{ $error ? 'border-red-500' : '' }}"
>
    <!-- Toolbar -->
    <div x-show="isInitialized" class="border-b border-gray-200 bg-gray-50 p-2">
        <div class="flex flex-wrap items-center gap-1">
            <!-- Bold -->
            <button
                type="button"
                @click="editor && editor.chain().focus().toggleBold().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('bold') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Bold (Ctrl+B)"
                :disabled="!editor || disabled"
            >
                <x-icons.lucide.bold class="w-4 h-4" />
            </button>

            <!-- Italic -->
            <button
                type="button"
                @click="editor && editor.chain().focus().toggleItalic().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('italic') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Italic (Ctrl+I)"
                :disabled="!editor || disabled"
            >
                <x-icons.lucide.italic class="w-4 h-4" />
            </button>

            <!-- Underline -->
            <button
                type="button"
                @click="editor && editor.chain().focus().toggleUnderline().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('underline') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Underline (Ctrl+U)"
                :disabled="!editor || disabled"
            >
                <x-icons.lucide.underline class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Bullet List -->
            <button
                type="button"
                @click="editor && editor.chain().focus().toggleBulletList().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('bulletList') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Bullet List"
                :disabled="!editor || disabled"
            >
                <x-icons.lucide.list class="w-4 h-4" />
            </button>

            <!-- Numbered List -->
            <button
                type="button"
                @click="editor && editor.chain().focus().toggleOrderedList().run()"
                :class="{ 'bg-gray-200': editor && editor.isActive('orderedList') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Numbered List"
                :disabled="!editor || disabled"
            >
                <x-icons.lucide.list-ordered class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Link -->
            <button
                type="button"
                @click="toggleLink()"
                :class="{ 'bg-gray-200': editor && editor.isActive('link') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Add Link"
                :disabled="!editor || disabled"
            >
                <x-icons.lucide.link class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Undo -->
            <button
                type="button"
                @click="editor && editor.chain().focus().undo().run()"
                :disabled="!editor || disabled || !editor.can().undo()"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Undo (Ctrl+Z)"
            >
                <x-icons.lucide.undo class="w-4 h-4" />
            </button>

            <!-- Redo -->
            <button
                type="button"
                @click="editor && editor.chain().focus().redo().run()"
                :disabled="!editor || disabled || !editor.can().redo()"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Redo (Ctrl+Y)"
            >
                <x-icons.lucide.redo class="w-4 h-4" />
            </button>
        </div>
    </div>

    <!-- Loading State -->
    <div x-show="!isInitialized" class="min-h-[150px] flex items-center justify-center bg-gray-50">
        <div class="flex items-center space-x-2 text-gray-500">
            <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span class="text-sm">Loading editor...</span>
        </div>
    </div>

    <!-- Editor Content -->
    <div
        x-show="isInitialized"
        x-ref="editorElement"
        class="min-h-[150px] p-1 text-sm/6 text-gray-700 focus-within:ring-0 overflow-y-auto max-h-[200px]"
    ></div>

    <!-- Hidden input for Livewire binding -->
    @if($wireModel)
        <input type="hidden" wire:model.defer="{{ $wireModel }}" x-ref="hiddenInput" />
    @endif

    <!-- Error Message -->
    @if($error && $errorMessage)
        <div class="text-red-500 text-sm mt-1 px-4 pb-2">
            {{ $errorMessage }}
        </div>
    @endif
</div>



<style>
/* Basic editor styling */
.rich-text-editor .ProseMirror {
    outline: none;
    min-height: 120px;
    padding: 0.5rem;
}

/* Placeholder styling */
.rich-text-editor .ProseMirror p.is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #9ca3af;
    pointer-events: none;
    height: 0;
}

/* Basic list styling */
.rich-text-editor .ProseMirror ul {
    list-style-type: disc;
    margin-left: 1.5rem;
}

.rich-text-editor .ProseMirror ol {
    list-style-type: decimal;
    margin-left: 1.5rem;
}

.rich-text-editor .ProseMirror li {
    margin: 0.25rem 0;
}

/* Focus styles */
.rich-text-editor:focus-within {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}
</style>
