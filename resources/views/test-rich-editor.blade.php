<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rich Text Editor Test</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Rich Text Editor Test</h1>
        
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h2 class="text-xl font-semibold mb-4">Test Rich Text Editor Component</h2>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Rich Text Editor (Standalone)
                    </label>
                    <x-ui.rich-text-editor 
                        placeholder="Start typing to test the rich text editor..."
                        id="test-editor-1"
                    />
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Rich Text Editor (Disabled)
                    </label>
                    <x-ui.rich-text-editor 
                        placeholder="This editor is disabled"
                        :disabled="true"
                        id="test-editor-2"
                    />
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Rich Text Editor (With Error)
                    </label>
                    <x-ui.rich-text-editor 
                        placeholder="This editor has an error state"
                        :error="true"
                        error-message="This field is required"
                        id="test-editor-3"
                    />
                </div>
            </div>
            
            <div class="mt-8 p-4 bg-gray-50 rounded">
                <h3 class="font-semibold mb-2">Features to Test:</h3>
                <ul class="list-disc list-inside space-y-1 text-sm text-gray-600">
                    <li>Bold (Ctrl+B or toolbar button)</li>
                    <li>Italic (Ctrl+I or toolbar button)</li>
                    <li>Underline (Ctrl+U or toolbar button)</li>
                    <li>Bullet lists</li>
                    <li>Numbered lists</li>
                    <li>Links (toolbar button)</li>
                    <li>Undo/Redo (Ctrl+Z/Ctrl+Y or toolbar buttons)</li>
                    <li>Placeholder text</li>
                    <li>Disabled state</li>
                    <li>Error state</li>
                </ul>
            </div>

            <div class="mt-8 p-4 bg-blue-50 rounded">
                <h3 class="font-semibold mb-2 text-blue-800">Debug Information:</h3>
                <div class="text-sm text-blue-700">
                    <p>Open browser console (F12) to see debug logs and any errors.</p>
                    <p>The editor should show a loading spinner initially, then display the toolbar when ready.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
