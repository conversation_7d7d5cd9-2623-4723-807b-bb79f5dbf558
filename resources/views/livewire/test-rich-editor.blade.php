<div class="max-w-4xl mx-auto p-8">
    <h1 class="text-3xl font-bold mb-8">Rich Text Editor Livewire Test</h1>
    
    @if (session()->has('message'))
        <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            {{ session('message') }}
        </div>
    @endif
    
    @if (session()->has('content_preview'))
        <div class="mb-6 p-4 bg-gray-100 border border-gray-300 rounded">
            <h3 class="font-semibold mb-2">Submitted Content (HTML):</h3>
            <pre class="text-sm bg-white p-2 rounded border overflow-x-auto">{{ session('content_preview') }}</pre>
            
            <h3 class="font-semibold mb-2 mt-4">Rendered Content:</h3>
            <div class="prose max-w-none bg-white p-4 rounded border">
                {!! session('content_preview') !!}
            </div>
        </div>
    @endif
    
    <form wire:submit="submit" class="space-y-6">
        <div>
            <label class="block text-lg font-medium text-gray-900 mb-2">
                Rich Text Editor with Livewire (wire:model.defer)
                <span class="text-red-500">*</span>
            </label>
            <x-ui.rich-text-editor 
                wire-model="content"
                placeholder="Type your content here and test all features..."
                :error="$errors->has('content')"
                :error-message="$errors->first('content')"
                id="livewire-test-editor"
            />
        </div>
        
        <div>
            <label class="block text-lg font-medium text-gray-900 mb-2">
                Pre-filled Content Editor
            </label>
            <x-ui.rich-text-editor 
                wire-model="content2"
                placeholder="This should have pre-filled content..."
                id="prefilled-editor"
            />
        </div>
        
        <div class="flex items-center space-x-4">
            <button 
                type="submit" 
                class="px-6 py-2 bg-emerald-600 text-white font-semibold rounded-lg hover:bg-emerald-700 transition-colors"
                wire:loading.attr="disabled"
            >
                <span wire:loading.remove>Submit Content</span>
                <span wire:loading>Submitting...</span>
            </button>
            
            <button 
                type="button" 
                wire:click="$set('content', '')"
                class="px-4 py-2 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 transition-colors"
            >
                Clear Content
            </button>
        </div>
    </form>
    
    <div class="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h3 class="font-semibold mb-2 text-yellow-800">Live Content Preview:</h3>
        <div class="text-sm text-yellow-700 mb-2">
            Content Length: <span class="font-mono">{{ strlen($content) }}</span> characters
        </div>
        <div class="bg-white p-3 rounded border text-sm">
            <strong>Raw HTML:</strong>
            <pre class="mt-1 text-xs">{{ $content ?: 'No content yet...' }}</pre>
        </div>
        @if($content)
            <div class="bg-white p-3 rounded border mt-2">
                <strong>Rendered:</strong>
                <div class="prose max-w-none mt-1">
                    {!! $content !!}
                </div>
            </div>
        @endif
    </div>
    
    <div class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded">
        <h3 class="font-semibold mb-2 text-blue-800">Testing Instructions:</h3>
        <ol class="list-decimal list-inside space-y-1 text-sm text-blue-700">
            <li>Type some content in the editor</li>
            <li>Use toolbar buttons to format text (bold, italic, underline)</li>
            <li>Create bullet and numbered lists</li>
            <li>Add links using the link button</li>
            <li>Test undo/redo functionality</li>
            <li>Watch the live preview update as you type</li>
            <li>Submit the form to test Livewire integration</li>
            <li>Check browser console for any errors</li>
        </ol>
    </div>
</div>
